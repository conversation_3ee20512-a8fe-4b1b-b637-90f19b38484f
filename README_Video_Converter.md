# MP3 to Video Converter

Sad<PERSON> sk<PERSON>tov na konverziu MP3 súborov z priečinka "Krvavý Audio" na videá.

## 📁 Súbory

### 1. `mp3_to_video_converter.sh` - <PERSON><PERSON><PERSON><PERSON> konverter
- **Interaktívny skript** s menu pre výber typu videa
- Podporuje rôzne typy pozadia a efektov
- Možnosti:
  - <PERSON><PERSON><PERSON><PERSON> (čierne, tmavo červené)
  - Spektrogram (dúhové farby)
  - Waveform (vlny)
  - Vlastný obrázok na pozadí

### 2. `quick_mp3_to_video.sh` - Rýchla konverzia
- **Automatická konverzia** bez otázok
- Tmavo červené pozadie (vhodné pre horror obsah)
- Najrychlejšia možnosť

### 3. `spectrum_video_converter.sh` - Spektrogram videá
- **Špecializovaný** na spektrogram efekty
- R<PERSON><PERSON><PERSON> farebn<PERSON> schémy
- Ide<PERSON>lne pre YouTube a sociálne siete

## 🚀 Použitie

### Rýchla konverzia (odporúčané pre začiatok):
```bash
./quick_mp3_to_video.sh
```

### Interaktívna konverzia s možnosťami:
```bash
./mp3_to_video_converter.sh
```

### Spektrogram videá:
```bash
./spectrum_video_converter.sh
```

## 📂 Štruktúra

```
Vstup:  ~/Desktop/Krvavý Audio/*.mp3
Výstup: ./Krvavý_Audio_Videos/*.mp4
        ./Krvavý_Audio_Spectrum_Videos/*.mp4
```

## ⚙️ Nastavenia

- **Rozlíšenie**: 1920x1080 (Full HD)
- **Video kodek**: H.264
- **Audio kodek**: AAC
- **Framerate**: 25 fps

## 🎨 Typy videí

### 1. Farebné pozadie
- Jednofarebné pozadie s audio stopou
- Vhodné pre podcasty a rozprávky

### 2. Spektrogram
- Vizualizácia frekvenčného spektra
- Dynamické farby reagujúce na hudbu
- Veľmi atraktívne pre YouTube

### 3. Waveform
- Zobrazenie zvukových vĺn
- Minimalistický dizajn

### 4. Obrázok na pozadí
- Statický obrázok s audio stopou
- Možnosť použiť vlastné obrázky

## 🔧 Požiadavky

- **FFmpeg** (už nainštalovaný)
- **Bash** shell
- **macOS/Linux**

## 📊 Výkon

- Jeden 30-minútový MP3 súbor = cca 2-5 minút spracovania
- Spektrogram videá trvajú dlhšie (komplexnejšie efekty)
- Farebné pozadie je najrychlejšie

## 🎯 Tipy

1. **Pre YouTube**: Použite spektrogram videá
2. **Pre rýchle zdieľanie**: Použite quick_mp3_to_video.sh
3. **Pre profesionálny vzhľad**: Použite vlastný obrázok na pozadí
4. **Pre horror obsah**: Tmavo červené pozadie je ideálne

## 🚨 Riešenie problémov

### Ak sa skript nespustí:
```bash
chmod +x *.sh
```

### Ak chýba FFmpeg:
```bash
brew install ffmpeg
```

### Ak je výstup prázdny:
- Skontrolujte cestu k MP3 súborom
- Skontrolujte oprávnenia priečinkov

## 📈 Štatistiky

Aktuálne v priečinku "Krvavý Audio":
- **87 MP3 súborov** pripravených na konverziu
- Celková veľkosť: ~2.5 GB audio obsahu
- Odhadovaný čas spracovania: 3-6 hodín (všetky súbory)

---

**Vytvorené pomocou Augment Agent** 🤖
