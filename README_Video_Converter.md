# 🎬 MP3 to Video Converter s 16:9 Obrázkami

Kompletná sada nástrojov na konverziu MP3 súborov z priečinka "Krvavý Audio" na profesionálne videá s 16:9 obrázkami.

## 🚀 <PERSON><PERSON><PERSON><PERSON> štart

```bash
# Spustite kompletný workflow
./complete_mp3_to_video_workflow.sh
```

## 📁 Súbory a nástroje

### 🎨 **Generovanie obrázkov 16:9**

#### 1. `generate_16_9_images.py` - Pokročilý generátor
- **Python skript** s AI integráciou
- Podporuje OpenAI DALL-E API
- Gradientné pozadia s textom
- Automatické prompty na základe názvov súborov

#### 2. `create_simple_backgrounds.sh` - Jednoduché pozadia
- **Rýchle vytvorenie** základných pozadí
- 6 r<PERSON><PERSON><PERSON><PERSON> (horror, mystické, klasické)
- Používa FFmpeg na generovanie

### 🎬 **Konverzia na videá**

#### 3. `mp3_to_video_converter.sh` - Hlavný konverter
- **Interaktívny skript** s rozšírenými možnosťami
- Podporuje auto-použitie vygenerovaných obrázkov
- 8 rôznych typov videí
- Možnosti:
  - Farebné pozadie (čierne, tmavo červené)
  - Spektrogram (dúhové farby)
  - Waveform (vlny)
  - Vlastný obrázok na pozadí
  - **NOVÉ:** Auto-použitie 16:9 obrázkov
  - **NOVÉ:** Náhodné pozadia

#### 4. `quick_mp3_to_video.sh` - Rýchla konverzia
- **Automatická konverzia** bez otázok
- Tmavo červené pozadie (vhodné pre horror obsah)
- Najrychlejšia možnosť

#### 5. `spectrum_video_converter.sh` - Spektrogram videá
- **Špecializovaný** na spektrogram efekty
- 4 rôzne farebné schémy
- Ideálne pre YouTube a sociálne siete

### 🔄 **Workflow automatizácia**

#### 6. `complete_mp3_to_video_workflow.sh` - Hlavný workflow
- **Kompletná automatizácia** celého procesu
- Interaktívne menu s 10 možnosťami
- Kontrola požiadaviek
- Štatistiky projektu

## 🚀 Použitie

### 🎯 **Odporúčané workflow (najjednoduchšie):**
```bash
# Spustite hlavný workflow a vyberte možnosť 7 alebo 8
./complete_mp3_to_video_workflow.sh
```

### 🎨 **Len generovanie obrázkov:**
```bash
# Jednoduché pozadia
./create_simple_backgrounds.sh

# Pokročilé obrázky s textom
python3 generate_16_9_images.py

# OpenAI DALL-E obrázky (potrebný API kľúč)
python3 generate_16_9_images.py  # vyberte možnosť 3
```

### 🎬 **Len konverzia na videá:**
```bash
# Rýchla konverzia (tmavo červené pozadie)
./quick_mp3_to_video.sh

# Spektrogram videá
./spectrum_video_converter.sh

# Interaktívny konverter (všetky možnosti)
./mp3_to_video_converter.sh
```

### 🔄 **Kompletné workflow možnosti:**
```bash
# Pozadia + Rýchle videá
./complete_mp3_to_video_workflow.sh  # možnosť 7

# Pozadia + Spektrogram videá
./complete_mp3_to_video_workflow.sh  # možnosť 8

# Python obrázky + Videá s obrázkami
./complete_mp3_to_video_workflow.sh  # možnosť 9
```

## 📂 Štruktúra

```
Vstup:  ~/Desktop/Krvavý Audio/*.mp3
Výstup: ./Krvavý_Audio_Videos/*.mp4
        ./Krvavý_Audio_Spectrum_Videos/*.mp4
```

## ⚙️ Nastavenia

- **Rozlíšenie**: 1920x1080 (Full HD)
- **Video kodek**: H.264
- **Audio kodek**: AAC
- **Framerate**: 25 fps

## 🎨 Typy videí

### 1. Farebné pozadie
- Jednofarebné pozadie s audio stopou
- Vhodné pre podcasty a rozprávky

### 2. Spektrogram
- Vizualizácia frekvenčného spektra
- Dynamické farby reagujúce na hudbu
- Veľmi atraktívne pre YouTube

### 3. Waveform
- Zobrazenie zvukových vĺn
- Minimalistický dizajn

### 4. Obrázok na pozadí
- Statický obrázok s audio stopou
- Možnosť použiť vlastné obrázky

## 🔧 Požiadavky

- **FFmpeg** (už nainštalovaný)
- **Bash** shell
- **macOS/Linux**

## 📊 Výkon

- Jeden 30-minútový MP3 súbor = cca 2-5 minút spracovania
- Spektrogram videá trvajú dlhšie (komplexnejšie efekty)
- Farebné pozadie je najrychlejšie

## 🎯 Tipy

1. **Pre YouTube**: Použite spektrogram videá
2. **Pre rýchle zdieľanie**: Použite quick_mp3_to_video.sh
3. **Pre profesionálny vzhľad**: Použite vlastný obrázok na pozadí
4. **Pre horror obsah**: Tmavo červené pozadie je ideálne

## 🚨 Riešenie problémov

### Ak sa skript nespustí:
```bash
chmod +x *.sh
```

### Ak chýba FFmpeg:
```bash
brew install ffmpeg
```

### Ak je výstup prázdny:
- Skontrolujte cestu k MP3 súborom
- Skontrolujte oprávnenia priečinkov

## 📈 Štatistiky

Aktuálne v priečinku "Krvavý Audio":
- **87 MP3 súborov** pripravených na konverziu
- Celková veľkosť: ~2.5 GB audio obsahu
- Odhadovaný čas spracovania: 3-6 hodín (všetky súbory)

---

**Vytvorené pomocou Augment Agent** 🤖
