#!/usr/bin/env python3
"""
Generá<PERSON> o<PERSON>r<PERSON> pomocou Gradio Midjourney API
Vytvorí obrázky pre horror audio súbory a následne videá
"""

import os
import sys
import time
import requests
from gradio_client import Client

# Farby pre výstup
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'

def print_color(color, text):
    print(f"{color}{text}{Colors.NC}")

def create_horror_prompt(filename):
    """Vytvorí horror prompt na základe názvu súboru"""
    # Odstránenie prípony a emoji
    clean_name = filename.replace('.mp3', '').replace('🔪', '').strip()
    
    # Základný horror prompt s 16:9 špecifikáciou
    base_prompt = "cinematic horror scene, dark atmospheric, gothic style, 16:9 aspect ratio, high quality, detailed, "
    
    # Špecifické prompty na základe názvu
    if 'upír' in clean_name.lower() or 'vampire' in clean_name.lower():
        return base_prompt + "vampire castle at night, blood moon, gothic architecture, dark red atmosphere, ancient stone walls, dramatic lighting"
    elif 'les' in clean_name.lower() or 'forest' in clean_name.lower():
        return base_prompt + "dark haunted forest, twisted ancient trees, thick fog, moonlight filtering through branches, eerie shadows"
    elif 'hrad' in clean_name.lower() or 'castle' in clean_name.lower():
        return base_prompt + "medieval haunted castle, storm clouds, lightning strikes, gothic towers, crumbling stone walls"
    elif 'duch' in clean_name.lower() or 'ghost' in clean_name.lower():
        return base_prompt + "ghostly apparition, abandoned mansion, ethereal mist, supernatural atmosphere, floating spirits"
    elif 'mlyn' in clean_name.lower() or 'mill' in clean_name.lower():
        return base_prompt + "old abandoned watermill, dark flowing water, moss-covered stones, eerie atmosphere, rustic decay"
    elif 'bezruká' in clean_name.lower():
        return base_prompt + "mysterious figure in dark robes, ancient curse, gothic horror, shadowy silhouette, supernatural presence"
    elif 'babylon' in clean_name.lower():
        return base_prompt + "ancient babylon ruins, mystical symbols, dark rituals, occult atmosphere, crumbling ziggurats"
    elif 'čierna voda' in clean_name.lower():
        return base_prompt + "dark black water, mysterious lake, fog rising from surface, ominous reflections, gothic landscape"
    else:
        return base_prompt + f"mysterious dark scene inspired by '{clean_name}', horror atmosphere, gothic elements, cinematic composition"

def generate_image_with_gradio(prompt, filename_base):
    """Generuje obrázok pomocou Gradio Midjourney API"""
    try:
        print_color(Colors.BLUE, f"  Pripájam sa k Midjourney API...")
        client = Client("Dagfinn1962/Midjourney-Free")
        
        # Negatívny prompt pre lepšiu kvalitu
        negative_prompt = "(deformed iris, deformed pupils, semi-realistic, cgi, 3d, render, sketch, cartoon, drawing, anime:1.4), text, close up, cropped, out of frame, worst quality, low quality, jpeg artifacts, ugly, duplicate, morbid, mutilated, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, blurry, dehydrated, bad anatomy, bad proportions, extra limbs, cloned face, disfigured, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, fused fingers, too many fingers, long neck"
        
        print_color(Colors.BLUE, f"  Generujem obrázok...")
        print_color(Colors.CYAN, f"  Prompt: {prompt[:80]}...")
        
        result = client.predict(
            prompt=prompt,
            negative_prompt=negative_prompt,
            use_negative_prompt=True,
            style="2560 x 1440",  # 16:9 pomer
            seed=0,
            width=1024,
            height=576,  # 16:9 pomer pre 1024 šírku
            guidance_scale=6,
            randomize_seed=True,
            api_name="/run"
        )
        
        print_color(Colors.GREEN, f"  ✓ API volanie úspešné")
        return result
        
    except Exception as e:
        print_color(Colors.RED, f"  ✗ Chyba pri generovaní: {e}")
        return None

def download_image(image_url, output_path):
    """Stiahne obrázok z URL"""
    try:
        print_color(Colors.BLUE, f"  Sťahujem obrázok...")
        response = requests.get(image_url, timeout=30)
        if response.status_code == 200:
            with open(output_path, 'wb') as f:
                f.write(response.content)
            print_color(Colors.GREEN, f"  ✓ Obrázok uložený: {os.path.basename(output_path)}")
            return True
        else:
            print_color(Colors.RED, f"  ✗ Chyba pri sťahovaní: HTTP {response.status_code}")
            return False
    except Exception as e:
        print_color(Colors.RED, f"  ✗ Chyba pri sťahovaní: {e}")
        return False

def create_video_with_image(mp3_file, image_file, output_file):
    """Vytvorí video z MP3 a obrázka pomocou FFmpeg"""
    print_color(Colors.BLUE, f"  Vytváram video...")
    
    cmd = [
        'ffmpeg',
        '-loop', '1',
        '-i', image_file,
        '-i', mp3_file,
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-vf', 'scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2',
        '-shortest',
        '-y', output_file,
        '-loglevel', 'error'
    ]
    
    import subprocess
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print_color(Colors.GREEN, f"  ✓ Video vytvorené: {os.path.basename(output_file)}")
            return True
        else:
            print_color(Colors.RED, f"  ✗ Chyba pri vytváraní videa: {result.stderr}")
            return False
    except Exception as e:
        print_color(Colors.RED, f"  ✗ Chyba pri spúšťaní FFmpeg: {e}")
        return False

def main():
    print_color(Colors.PURPLE, "=== Gradio Midjourney Image & Video Generator ===")
    print_color(Colors.CYAN, "Generuje AI obrázky a videá pre horror audio súbory")
    print()
    
    # Nastavenia
    INPUT_DIR = os.path.expanduser("~/Desktop/Krvavý Audio")
    IMAGES_DIR = "./Midjourney_Images_16_9"
    VIDEOS_DIR = "./Midjourney_Videos"
    
    # Vytvorenie výstupných priečinkov
    os.makedirs(IMAGES_DIR, exist_ok=True)
    os.makedirs(VIDEOS_DIR, exist_ok=True)
    
    print_color(Colors.YELLOW, f"Vstupný priečinok: {INPUT_DIR}")
    print_color(Colors.YELLOW, f"Obrázky: {IMAGES_DIR}")
    print_color(Colors.YELLOW, f"Videá: {VIDEOS_DIR}")
    print()
    
    # Získanie zoznamu MP3 súborov
    if not os.path.exists(INPUT_DIR):
        print_color(Colors.RED, f"Priečinok {INPUT_DIR} neexistuje!")
        return
    
    mp3_files = [f for f in os.listdir(INPUT_DIR) if f.endswith('.mp3')]
    if not mp3_files:
        print_color(Colors.RED, f"Nenašli sa žiadne MP3 súbory v {INPUT_DIR}")
        return
    
    print_color(Colors.GREEN, f"Našiel som {len(mp3_files)} MP3 súborov")
    print()
    
    # Menu
    print_color(Colors.BLUE, "Vyberte možnosť:")
    print("1) Generovať len obrázky")
    print("2) Generovať obrázky + videá")
    print("3) Test s jedným súborom")
    print()
    choice = input("Zadajte číslo (1-3): ").strip()
    
    if choice == '3':
        # Test s prvým súborom
        mp3_files = mp3_files[:1]
        print_color(Colors.YELLOW, f"Testujem s: {mp3_files[0]}")
        print()
    
    # Spracovanie súborov
    success_count = 0
    for i, mp3_file in enumerate(mp3_files, 1):
        filename_base = mp3_file.replace('.mp3', '')
        mp3_path = os.path.join(INPUT_DIR, mp3_file)
        image_path = os.path.join(IMAGES_DIR, f"{filename_base}.jpg")
        video_path = os.path.join(VIDEOS_DIR, f"{filename_base}.mp4")
        
        print_color(Colors.YELLOW, f"[{i}/{len(mp3_files)}] {filename_base}")
        
        # Generovanie obrázka
        if not os.path.exists(image_path):
            prompt = create_horror_prompt(mp3_file)
            result = generate_image_with_gradio(prompt, filename_base)
            
            if result:
                # Spracovanie výsledku z Gradio API
                image_path_found = None

                if isinstance(result, tuple) and len(result) >= 1:
                    # Výsledok je tuple, prvý element obsahuje obrázky
                    images_data = result[0]
                    if isinstance(images_data, list) and len(images_data) > 0:
                        # Berieme prvý obrázok
                        first_image = images_data[0]
                        if isinstance(first_image, dict) and 'image' in first_image:
                            image_path_found = first_image['image']
                        elif isinstance(first_image, str):
                            image_path_found = first_image
                elif isinstance(result, str):
                    image_path_found = result

                if image_path_found:
                    print_color(Colors.BLUE, f"  Našiel som obrázok: {os.path.basename(image_path_found)}")

                    if image_path_found.startswith('http'):
                        # Je to URL, stiahni obrázok
                        if download_image(image_path_found, image_path):
                            success_count += 1
                        else:
                            print_color(Colors.RED, f"  ✗ Nepodarilo sa stiahnuť obrázok")
                            continue
                    else:
                        # Je to lokálna cesta
                        if os.path.exists(image_path_found):
                            import shutil
                            shutil.copy2(image_path_found, image_path)
                            print_color(Colors.GREEN, f"  ✓ Obrázok skopírovaný")
                            success_count += 1
                        else:
                            print_color(Colors.RED, f"  ✗ Obrázok sa nenašiel na: {image_path_found}")
                            continue
                else:
                    print_color(Colors.RED, f"  ✗ Nepodarilo sa extrahovať cestu k obrázku z výsledku")
                    print_color(Colors.YELLOW, f"  Debug: {str(result)[:200]}...")
                    continue
            else:
                print_color(Colors.RED, f"  ✗ Nepodarilo sa vygenerovať obrázok")
                continue
        else:
            print_color(Colors.GREEN, f"  ✓ Obrázok už existuje")
            success_count += 1
        
        # Vytvorenie videa (ak je požadované)
        if choice in ['2', '3'] and os.path.exists(image_path):
            if not os.path.exists(video_path):
                if create_video_with_image(mp3_path, image_path, video_path):
                    print_color(Colors.GREEN, f"  ✓ Kompletné!")
                else:
                    print_color(Colors.RED, f"  ✗ Video sa nepodarilo vytvoriť")
            else:
                print_color(Colors.GREEN, f"  ✓ Video už existuje")
        
        print()
        
        # Pauza medzi požiadavkami (aby sme nepretažili API)
        if i < len(mp3_files):
            print_color(Colors.BLUE, "  Čakám 3 sekundy...")
            time.sleep(3)
    
    print_color(Colors.GREEN, "=== Spracovanie dokončené! ===")
    print_color(Colors.YELLOW, f"Úspešne spracovaných: {success_count}/{len(mp3_files)} súborov")
    
    if choice in ['2', '3']:
        print_color(Colors.BLUE, f"Obrázky: {IMAGES_DIR}")
        print_color(Colors.BLUE, f"Videá: {VIDEOS_DIR}")
    else:
        print_color(Colors.BLUE, f"Obrázky: {IMAGES_DIR}")

if __name__ == "__main__":
    # Kontrola závislostí
    try:
        from gradio_client import Client
        import requests
    except ImportError as e:
        print_color(Colors.RED, f"Chýbajú závislosti: {e}")
        print_color(Colors.YELLOW, "Nainštalujte ich pomocou:")
        print_color(Colors.BLUE, "pip install gradio_client requests")
        sys.exit(1)
    
    main()
